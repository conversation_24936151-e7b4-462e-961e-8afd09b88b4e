import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

// CORS headers to allow cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

serve(async (req) => {
  console.log("=== STRIPE WEBHOOK TEST FUNCTION ===");
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }

  try {
    // 1. Log the STRIPE_WEBHOOK_SECRET environment variable (masked for security)
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    console.log(`STRIPE_WEBHOOK_SECRET configured: ${webhookSecret ? "YES (value exists)" : "NO (missing)"}`);
    if (webhookSecret) {
      // Only show first and last few characters for security
      const maskedSecret = webhookSecret.length > 8 
        ? `${webhookSecret.substring(0, 4)}...${webhookSecret.substring(webhookSecret.length - 4)}`
        : "(too short to mask safely)";
      console.log(`STRIPE_WEBHOOK_SECRET format: ${maskedSecret}`);
      console.log(`STRIPE_WEBHOOK_SECRET length: ${webhookSecret.length} characters`);
    }

    // 2. Log the incoming webhook request headers
    console.log("=== REQUEST HEADERS ===");
    const headers: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headers[key] = value;
      console.log(`${key}: ${key.toLowerCase() === 'authorization' ? '[REDACTED]' : value}`);
    });

    // Specifically check for stripe-signature header
    const signature = req.headers.get("stripe-signature");
    console.log(`\nStripe signature header present: ${signature ? "YES" : "NO"}`);
    if (signature) {
      console.log(`Stripe signature value: ${signature}`);
      
      // Parse the signature to check its components
      try {
        const components = signature.split(',').reduce((acc, part) => {
          const [key, value] = part.split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
        
        console.log("Signature components:", components);
      } catch (err) {
        console.log("Failed to parse signature components:", err.message);
      }
    }

    // 3. Log the raw request body
    const rawBody = await req.text();
    console.log("\n=== RAW REQUEST BODY ===");
    console.log(rawBody);
    
    // Try to parse the body as JSON for better logging
    try {
      const jsonBody = JSON.parse(rawBody);
      console.log("\n=== PARSED JSON BODY ===");
      console.log("Event type:", jsonBody.type);
      console.log("Event ID:", jsonBody.id);
      console.log("API version:", jsonBody.api_version);
    } catch (err) {
      console.log("Failed to parse body as JSON:", err.message);
    }

    // 4. Attempt signature verification using manual crypto verification
    if (webhookSecret && signature) {
      console.log("\n=== ATTEMPTING SIGNATURE VERIFICATION ===");
      try {
        // Parse the signature header
        const elements = signature.split(',');
        const signatureElements: Record<string, string> = {};

        for (const element of elements) {
          const [key, value] = element.split('=');
          signatureElements[key] = value;
        }

        const timestamp = signatureElements.t;
        const v1Signature = signatureElements.v1;

        if (!timestamp || !v1Signature) {
          throw new Error('Invalid signature format');
        }

        console.log(`Timestamp: ${timestamp}`);
        console.log(`v1 Signature: ${v1Signature}`);

        // Create the signed payload
        const signedPayload = `${timestamp}.${rawBody}`;

        // Verify the signature using Web Crypto API
        const encoder = new TextEncoder();
        const key = await crypto.subtle.importKey(
          'raw',
          encoder.encode(webhookSecret),
          { name: 'HMAC', hash: 'SHA-256' },
          false,
          ['sign']
        );

        const signature_bytes = await crypto.subtle.sign(
          'HMAC',
          key,
          encoder.encode(signedPayload)
        );

        // Convert to hex string
        const expectedSignature = Array.from(new Uint8Array(signature_bytes))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');

        console.log(`Expected signature: ${expectedSignature}`);
        console.log(`Received signature: ${v1Signature}`);

        // Compare signatures
        if (expectedSignature === v1Signature) {
          console.log("✅ Signature verification SUCCESSFUL!");

          // Parse the verified event
          const event = JSON.parse(rawBody);
          console.log("Verified event type:", event.type);
          console.log("Verified event ID:", event.id);
        } else {
          throw new Error('Signature mismatch');
        }

      } catch (err) {
        console.log("❌ Signature verification FAILED:", err.message);
        console.log("Detailed error:", err);

        // Continue processing despite verification failure
        console.log("Continuing despite verification failure for testing purposes");
      }
    }

    // 5. Return a success response for testing
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: "Webhook received successfully (TEST MODE - signature verification bypassed)",
        timestamp: new Date().toISOString(),
        headers: headers,
        bodyLength: rawBody.length,
      }),
      { 
        status: 200, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );

  } catch (error) {
    // Log any errors that occurred
    console.error("Error processing webhook:", error);
    
    return new Response(
      JSON.stringify({ 
        error: "Error processing webhook", 
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { 
          ...corsHeaders, 
          'Content-Type': 'application/json' 
        } 
      }
    );
  }
});